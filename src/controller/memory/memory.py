#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from urllib.parse import urlparse

from mem0 import AsyncMemory

from config import ES_URL, LLM_API_URL, LLM_API_KEY, LLMModel, EmbeddingModel


class MemoryController:
    """
    简易丐版封装,显然光靠向量还是有点扯淡的
    """
    def __init__(self):
        self.memory_client: AsyncMemory | None = None

    async def get_memory(self) -> AsyncMemory:
        """获取 memory 实例，如果不存在则初始化"""
        if self.memory_client is None:
            self.memory_client = await self.initialize()
        return self.memory_client

    @staticmethod
    async def initialize():
        parsed = urlparse(ES_URL)
        config = {
            "version": "v1.0",
            "vector_store": {
                "provider": "elasticsearch",
                "config": {
                    "collection_name": "mem0",
                    "host": f"{parsed.scheme}://{parsed.hostname}",
                    "port": parsed.port,
                    "user": parsed.username,
                    "password": parsed.password,
                    "embedding_model_dims": 1024
                }
            },
            "llm": {
                "provider": "openai",
                "config": {
                    "api_key": LLM_API_KEY,
                    "openai_base_url": LLM_API_URL,
                    "model": LLMModel.DEEPSEEK_CHAT
                }
            },
            "embedder": {
                "provider": "openai",
                "config": {
                    "api_key": LLM_API_KEY,  # 不使用真实 key
                    "openai_base_url": LLM_API_URL,
                    "model": EmbeddingModel.BGE_M3
                }
            }
        }
        return await AsyncMemory.from_config(config)

    async def add(self, user: str, assistant: str, user_id: int):
        memory = await self.get_memory()
        await memory.add(
            messages=[{"role": "user", "content": user}, {"role": "assistant", "content": assistant}],
            user_id=str(user_id))

    async def search(self, query: str, user_id: int, limit: int = 3, threshold: float = 0.8):
        memory = await self.get_memory()
        res = await memory.search(query=query, user_id=str(user_id), limit=limit, threshold=threshold)
        memories = [m["memory"] for m in res["results"]]
        return memories

    async def get_all(self, user_id: int):
        memory = await self.get_memory()
        all_memories = await memory.get_all(user_id="default_user")
        return await all_memories["results"]

    async def delete(self, user_id: int = None, memory_id: str = None):
        memory = await self.get_memory()
        if memory_id is not None:
            res = await memory.delete(memory_id=memory_id)
        elif user_id is not None:
            res = await memory.delete_all(user_id=str(user_id))
        else:
            raise KeyError("user_id或memory_id不能为空")

        return res


Memory = MemoryController()
