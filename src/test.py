import json
import httpx
from tavily import AsyncTavily<PERSON>lient

from config import TVLY_APIKEY

client = AsyncTavilyClient(TVLY_APIKEY)
async def web_search():
    response = await client.search(
        query="浦银理财是做什么的？",
        max_results=10
    )
    print(json.dumps(response, ensure_ascii=False, indent=4))


async def web_extract():
    response = await client.extract(
        urls=["https://www.spdb-wm.com/"]
    )
    print(json.dumps(response, ensure_ascii=False, indent=4))



async def web_extract_by_url():
    async with httpx.AsyncClient(transport=httpx.AsyncHTTPTransport(retries=3)) as client:
        response = await client.get("https://www.spdb-wm.com/", follow_redirects=True)
        return response.content



if __name__ == '__main__':
    import asyncio
    asyncio.run(web_extract_by_url())