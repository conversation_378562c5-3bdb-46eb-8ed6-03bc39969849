#!/usr/bin/env python3
# -*- coding: utf-8 -*-
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import base64
import httpx
import asyncio
from pathlib import Path


async def source(filepath: Path):
    with open(filepath.as_posix(), mode="rb") as f:
        pdf_data = f.read()
        b64_data = base64.b64encode(pdf_data).decode("utf-8")

    async_client = httpx.AsyncClient(timeout=300.0)
    url = "http://121.36.246.234:5001/v1alpha/convert/source"
    payload = {
      "options": {
        "from_formats": ["docx", "pptx", "html", "image", "pdf", "asciidoc", "md", "xlsx"],
        "to_formats": ["html"],
        "image_export_mode": "embedded",
        "do_ocr": True,
        "force_ocr": True,
        "ocr_engine": "rapidocr",
        "ocr_lang": ["english","chinese"],
        "pdf_backend": "dlparse_v4",
        "table_mode": "accurate",
        "abort_on_error": False,
        "return_as_file": False,
      },
      "file_sources": [
          {
              "base64_string": b64_data,
              "filename": filepath.name
          }
      ]
    }

    response = await async_client.post(url, json=payload)

    data = response.json()
    with open(filepath.with_suffix(".html").as_posix(), mode="w", encoding="utf-8") as f:
        f.write(data["document"][0]["html_content"])



if __name__ == '__main__':
    asyncio.run(source(Path(r"C:\Users\<USER>\Downloads\F2139 - 贵州省国资委工作规则(2).docx")))